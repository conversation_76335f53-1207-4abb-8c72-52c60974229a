<?php
declare(strict_types=1);

namespace MadHat\Sitemap\Helper;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class CanonicalUrl
{
    /**
     * @var ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;

    /**
     * @var Configurable
     */
    private Configurable $configurableProduct;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @param ProductRepositoryInterface $productRepository
     * @param Configurable $configurableProduct
     * @param LoggerInterface $logger
     */
    public function __construct(
        ProductRepositoryInterface $productRepository,
        Configurable $configurableProduct,
        LoggerInterface $logger
    ) {
        $this->productRepository = $productRepository;
        $this->configurableProduct = $configurableProduct;
        $this->logger = $logger;
    }

    /**
     * Determine if a product URL should have a canonical link based on multi-page SEO strategy
     *
     * Strategy:
     * - Base products (configurable): NO canonical link, but INCLUDE in sitemap
     * - Variant products (simple with parent): Self-canonical link, INCLUDE in sitemap
     * - Standalone simple products: NO canonical link, INCLUDE in sitemap
     * - Other product types: NO canonical link, INCLUDE in sitemap
     *
     * @param string $url
     * @return array ['should_include' => bool, 'canonical_url' => string|null, 'product_type' => string]
     */
    public function getCanonicalStrategy(string $url): array
    {
        $defaultResult = [
            'should_include' => true,
            'canonical_url' => null,
            'product_type' => 'unknown'
        ];

        try {
            // Extract SKU from URL
            $sku = $this->extractSkuFromUrl($url);
            if (!$sku) {
                $this->logger->debug('Could not extract SKU from URL: ' . $url);
                return $defaultResult;
            }

            // Load product by SKU
            $product = $this->productRepository->get($sku);
            $productType = $product->getTypeId();

            $result = [
                'should_include' => true,
                'canonical_url' => null,
                'product_type' => $productType
            ];

            if ($productType === 'configurable') {
                // Base product: NO canonical link, but include in sitemap
                $result['canonical_url'] = null;
                $this->logger->debug("Base product (configurable) - no canonical: $url");
                
            } elseif ($productType === 'simple') {
                // Check if it's a variant of a configurable product
                $parentIds = $this->configurableProduct->getParentIdsByChild($product->getId());
                
                if (!empty($parentIds)) {
                    // Variant product: Self-canonical link
                    $result['canonical_url'] = $this->cleanUrl($url);
                    $this->logger->debug("Variant product (simple with parent) - self-canonical: $url");
                } else {
                    // Standalone simple product: NO canonical link
                    $result['canonical_url'] = null;
                    $this->logger->debug("Standalone simple product - no canonical: $url");
                }
            } else {
                // Other product types: NO canonical link
                $result['canonical_url'] = null;
                $this->logger->debug("Other product type ($productType) - no canonical: $url");
            }

            return $result;

        } catch (NoSuchEntityException $e) {
            $this->logger->debug('Product not found for URL: ' . $url);
            return $defaultResult;
        } catch (\Exception $e) {
            $this->logger->error('Error determining canonical strategy for URL ' . $url . ': ' . $e->getMessage());
            return $defaultResult;
        }
    }

    /**
     * Extract SKU from URL
     *
     * @param string $url
     * @return string|null
     */
    private function extractSkuFromUrl(string $url): ?string
    {
        // Remove leading slash if present
        $url = ltrim($url, '/');

        // Split by dash and get the last part (should be SKU)
        $urlParts = explode('-', $url);
        $potentialSku = end($urlParts);

        // Basic validation - SKU should be numeric for this system
        if (is_numeric($potentialSku)) {
            return $potentialSku;
        }

        return null;
    }

    /**
     * Clean URL for canonical use (remove query parameters, etc.)
     *
     * @param string $url
     * @return string
     */
    private function cleanUrl(string $url): string
    {
        // Remove query parameters
        $urlParts = parse_url($url);
        
        $cleanUrl = '';
        if (isset($urlParts['path'])) {
            $cleanUrl = $urlParts['path'];
        }
        
        // Remove leading slash for consistency
        return ltrim($cleanUrl, '/');
    }

    /**
     * Check if a product is a variant (simple product with configurable parent)
     *
     * @param int $productId
     * @return bool
     */
    public function isVariantProduct(int $productId): bool
    {
        try {
            $product = $this->productRepository->getById($productId);
            
            if ($product->getTypeId() !== 'simple') {
                return false;
            }

            $parentIds = $this->configurableProduct->getParentIdsByChild($productId);
            return !empty($parentIds);
            
        } catch (\Exception $e) {
            $this->logger->error('Error checking if product is variant: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a product is a base product (configurable)
     *
     * @param int $productId
     * @return bool
     */
    public function isBaseProduct(int $productId): bool
    {
        try {
            $product = $this->productRepository->getById($productId);
            return $product->getTypeId() === 'configurable';
            
        } catch (\Exception $e) {
            $this->logger->error('Error checking if product is base: ' . $e->getMessage());
            return false;
        }
    }
}

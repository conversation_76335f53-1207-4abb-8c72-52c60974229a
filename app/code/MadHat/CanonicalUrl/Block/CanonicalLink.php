<?php
declare(strict_types=1);

namespace MadHat\CanonicalUrl\Block;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Template;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;

class CanonicalLink extends Template
{
    /**
     * @var UrlInterface
     */
    protected UrlInterface $urlInterface;

    /**
     * @var RequestInterface
     */
    protected RequestInterface $request;

    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @param Template\Context $context
     * @param UrlInterface $urlInterface
     * @param RequestInterface $request
     * @param StoreManagerInterface $storeManager
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        UrlInterface $urlInterface,
        RequestInterface $request,
        StoreManagerInterface $storeManager,
        array $data = []
    ) {
        $this->urlInterface = $urlInterface;
        $this->request = $request;
        $this->storeManager = $storeManager;
        parent::__construct($context, $data);
    }

    /**
     * Add canonical url
     *
     * @return string|false
     * @throws NoSuchEntityException
     */
    public function getCanonicalUrl(): string|false
    {
        $allowedActions = [
            'cms_page_view',
            'contact_index_index',
            'cms_index_index',
            'customer_account_login',
            'customer_account_create'
        ];

        $fullActionName = $this->getRequest()->getFullActionName();
        if (!in_array($fullActionName, $allowedActions)) {
            return false;
        }

        $currentUrl = $this->urlInterface->getCurrentUrl();

        if ($fullActionName === 'cms_index_index') {
            $currentStoreCode = $this->storeManager->getStore()->getCode();
            if ($currentStoreCode == 'b2b') {
                return $this->storeManager->getDefaultStoreView()->getBaseUrl();
            }
            return $currentUrl;
        }

        if ($fullActionName === 'customer_account_login') {
            $currentUrl = preg_replace('/\/referer\/[^\/]+\//', '', $currentUrl);
        }

        $defaultStoreBaseUrl = $this->storeManager->getDefaultStoreView()->getBaseUrl();
        $currentBaseUrl = $this->storeManager->getStore()->getBaseUrl();
        $canonicalUrl = str_replace($currentBaseUrl, $defaultStoreBaseUrl, $currentUrl);

        return $canonicalUrl;
    }
}
